using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel文件记录管理窗体
    /// </summary>
    public partial class frmExcelFileManager : Form
    {
        private List<ExcelFileRecord> _currentRecords;

        /// <summary>
        /// 构造函数
        /// </summary>
        public frmExcelFileManager()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            // 设置ListView
            SetupListView();

            // 加载现有记录
            LoadFileRecords();
        }

        /// <summary>
        /// 设置ListView控件
        /// </summary>
        private void SetupListView()
        {
            listViewFiles.View = View.Details;
            listViewFiles.FullRowSelect = true;
            listViewFiles.GridLines = true;
            listViewFiles.MultiSelect = true;

            // 添加列
            listViewFiles.Columns.Add("文件名", 200);
            listViewFiles.Columns.Add("文件路径", 300);
            listViewFiles.Columns.Add("最后修改时间", 150);
        }

        /// <summary>
        /// 加载文件记录到ListView
        /// </summary>
        private void LoadFileRecords()
        {
            try
            {
                _currentRecords = ExcelFileRecordManager.LoadRecords();
                RefreshListView();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新ListView显示
        /// </summary>
        private void RefreshListView()
        {
            listViewFiles.Items.Clear();

            foreach (var record in _currentRecords.OrderByDescending(r => r.RecordTime))
            {
                var item = new ListViewItem(record.FileName);
                item.SubItems.Add(record.FilePath);
                item.SubItems.Add(record.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));
                item.Tag = record;
                listViewFiles.Items.Add(item);
            }

            // 自动调整列宽
            foreach (ColumnHeader column in listViewFiles.Columns)
            {
                column.AutoResize(ColumnHeaderAutoResizeStyle.ColumnContent);
            }
        }

        /// <summary>
        /// 更新状态标签
        /// </summary>
        private void UpdateStatusLabel()
        {
            toolStripStatusLabel1.Text = $"共 {_currentRecords.Count} 个文件记录";
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        /// <param name="message">状态信息</param>
        private void UpdateStatus(string message)
        {
            toolStripStatusLabel1.Text = message;
        }

        /// <summary>
        /// 记录当前打开的Excel文件
        /// </summary>
        private void 记录当前文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                var application = Globals.ThisAddIn.Application;
                var newRecords = new List<ExcelFileRecord>();

                foreach (Workbook workbook in application.Workbooks)
                {
                    try
                    {
                        // 只处理已保存的文件（有完整路径）
                        if (!string.IsNullOrEmpty(workbook.FullName) && File.Exists(workbook.FullName))
                        {
                            var fileInfo = new FileInfo(workbook.FullName);
                            var record = new ExcelFileRecord(workbook.FullName, fileInfo.LastWriteTime);
                            newRecords.Add(record);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理工作簿失败: {ex.Message}");
                    }
                }

                if (newRecords.Count > 0)
                {
                    ExcelFileRecordManager.AddRecords(newRecords);
                    LoadFileRecords();
                    UpdateStatus($"成功记录 {newRecords.Count} 个文件");
                }
                else
                {
                    UpdateStatus("没有找到可记录的Excel文件");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"记录文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开选定的文件
        /// </summary>
        private void 打开选定文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要打开的文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var filesToOpen = new List<string>();
                var successCount = 0;
                var failCount = 0;

                // 收集所有选定的文件路径
                foreach (ListViewItem item in listViewFiles.SelectedItems)
                {
                    if (item.Tag is ExcelFileRecord record)
                    {
                        filesToOpen.Add(record.FilePath);
                    }
                }

                // 逐个打开文件
                foreach (string filePath in filesToOpen)
                {
                    try
                    {
                        if (OpenExcelFile(filePath))
                        {
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        System.Diagnostics.Debug.WriteLine($"打开文件失败 {filePath}: {ex.Message}");
                    }
                }

                // 更新状态信息
                if (filesToOpen.Count == 1)
                {
                    if (successCount == 1)
                    {
                        UpdateStatus($"成功打开文件: {Path.GetFileName(filesToOpen[0])}");
                    }
                    else
                    {
                        UpdateStatus($"打开文件失败: {Path.GetFileName(filesToOpen[0])}");
                    }
                }
                else
                {
                    UpdateStatus($"打开完成 - 成功: {successCount} 个，失败: {failCount} 个");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除选定的文件记录
        /// </summary>
        private void 删除选定记录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("请先选择要删除的文件记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var recordsToRemove = new List<ExcelFileRecord>();
                foreach (ListViewItem item in listViewFiles.SelectedItems)
                {
                    if (item.Tag is ExcelFileRecord record)
                    {
                        recordsToRemove.Add(record);
                    }
                }

                ExcelFileRecordManager.RemoveRecords(recordsToRemove);
                LoadFileRecords();
                UpdateStatus($"成功删除 {recordsToRemove.Count} 个文件记录");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 一键清空所有记录
        /// </summary>
        private void 一键清空记录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (_currentRecords.Count == 0)
            {
                MessageBox.Show("当前没有文件记录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                ExcelFileRecordManager.ClearAllRecords();
                LoadFileRecords();
                UpdateStatus("已清空所有文件记录");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 双击列表项打开文件
        /// </summary>
        private void listViewFiles_DoubleClick(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 1)
            {
                var selectedItem = listViewFiles.SelectedItems[0];
                if (selectedItem.Tag is ExcelFileRecord record)
                {
                    OpenExcelFile(record.FilePath);
                }
            }
        }

        /// <summary>
        /// 打开Excel文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功打开文件</returns>
        private bool OpenExcelFile(string filePath)
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"文件不存在: {filePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                var application = Globals.ThisAddIn.Application;

                // 检查文件是否已经打开
                foreach (Workbook workbook in application.Workbooks)
                {
                    try
                    {
                        if (string.Equals(workbook.FullName, filePath, StringComparison.OrdinalIgnoreCase))
                        {
                            // 文件已打开，激活该工作簿
                            workbook.Activate();
                            UpdateStatus($"文件已打开: {Path.GetFileName(filePath)}");
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查工作簿失败: {ex.Message}");
                    }
                }

                // 文件未打开，打开文件
                application.Workbooks.Open(filePath);
                UpdateStatus($"成功打开文件: {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 关闭窗体
        /// </summary>
        private void 关闭ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}