﻿2025-07-29 09:28:38 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 09:28:38 [INFO] 配置文件实例已在加载时初始化
2025-07-29 09:28:38 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 09:28:38 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 09:28:38 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 09:28:38 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 09:28:38 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 09:28:38 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:28:38 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 09:28:38 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 09:28:38 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 09:28:38 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:28:38 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 09:28:38 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 09:28:38 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 09:28:38 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:28:38 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 09:28:38 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 09:28:38 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 09:28:38 [INFO] 成功初始化Excel应用程序实例
2025-07-29 09:28:39 [INFO] 自动备份路径未配置
2025-07-29 09:28:39 [DEBUG] 开始初始化授权控制器
2025-07-29 09:28:39 [DEBUG] 授权系统初始化完成，耗时: 361ms
2025-07-29 09:28:39 [DEBUG] 开始初始化授权验证
2025-07-29 09:28:39 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 09:28:39 [DEBUG] 权限管理器初始化成功
2025-07-29 09:28:39 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:28:39 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:28:39 [INFO] 开始初始化UI权限管理
2025-07-29 09:28:39 [DEBUG] [实例ID: 79805acb] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:28:39 [DEBUG] 🔍 [实例ID: 79805acb] 字典引用一致性检查:
2025-07-29 09:28:39 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:28:39 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:28:39 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:28:39 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:28:39 [DEBUG] 控件权限管理器初始化完成 [实例ID: 79805acb]
2025-07-29 09:28:39 [DEBUG] 开始注册控件权限映射
2025-07-29 09:28:39 [INFO] 开始初始化全局控件映射
2025-07-29 09:28:39 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 09:28:39 [DEBUG] 开始生成控件标题映射
2025-07-29 09:28:39 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:28:39 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:28:39 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:28:39 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 09:28:39 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 09:28:39 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:28:39 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 09:28:39 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 09:28:39 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 09:28:39 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 09:28:39 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 09:28:39 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 09:28:39 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 09:28:39 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 09:28:39 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 09:28:39 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 09:28:39 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 09:28:39 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 09:28:39 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 09:28:39 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 09:28:39 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 09:28:39 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 09:28:39 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 09:28:39 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 09:28:39 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 09:28:39 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 09:28:39 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 09:28:39 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 09:28:39 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 09:28:39 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 09:28:39 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 09:28:39 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 09:28:39 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 09:28:39 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 09:28:39 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 09:28:39 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 09:28:39 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 09:28:39 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 09:28:39 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 09:28:39 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 09:28:39 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 09:28:39 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 09:28:39 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 09:28:39 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 09:28:39 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 09:28:39 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 09:28:39 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 09:28:39 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 09:28:39 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 09:28:39 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 09:28:39 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 09:28:39 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 09:28:39 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 09:28:39 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 09:28:39 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 09:28:39 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 09:28:39 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 09:28:39 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 09:28:39 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 09:28:39 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 09:28:39 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 09:28:39 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 09:28:39 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 09:28:39 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 09:28:39 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 09:28:39 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 09:28:39 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 09:28:39 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 09:28:39 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 09:28:39 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 09:28:39 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 09:28:39 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 09:28:39 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 09:28:39 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 09:28:39 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 09:28:39 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 09:28:39 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 09:28:39 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 09:28:39 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 09:28:39 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 09:28:39 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 09:28:39 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 09:28:39 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 09:28:39 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 09:28:39 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 09:28:39 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 09:28:39 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 09:28:39 [DEBUG] 开始生成控件权限映射
2025-07-29 09:28:39 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:28:39 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:28:39 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:28:39 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:28:39 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:28:39 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:28:39 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:28:39 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:28:39 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 09:28:39 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 09:28:39 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 09:28:39 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 09:28:39 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 09:28:39 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:28:39 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:28:39 [INFO] 开始初始化权限验证
2025-07-29 09:28:39 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:28:39 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:28:39 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 09:28:39 [INFO] 启动网络授权信息获取任务
2025-07-29 09:28:39 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 09:28:39 [INFO] 所有权限检查完成
2025-07-29 09:28:39 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:28:39 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:39 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:39 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:39 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:39 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:39 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:28:39 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:28:39 [INFO] 权限验证初始化完成
2025-07-29 09:28:39 [INFO] UI权限管理初始化完成
2025-07-29 09:28:39 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:28:39 [INFO] 开始刷新控件标题
2025-07-29 09:28:39 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:28:39 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:28:39 [DEBUG] 控件标题刷新完成
2025-07-29 09:28:39 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:28:39 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:28:39 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:28:39 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:39 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:28:39 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:39 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:28:39 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:39 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:28:39 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:39 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:28:39 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:39 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:28:39 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:28:39 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:28:39 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:28:39 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:28:39 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:28:39 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:28:39 [INFO] 控件标题更正完成
2025-07-29 09:28:39 [INFO] 控件标题刷新完成
2025-07-29 09:28:39 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:28:39 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:28:39 [DEBUG] 授权验证初始化完成
2025-07-29 09:28:39 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 09:28:39 [INFO] 成功加载配置和授权信息
2025-07-29 09:28:39 [INFO] 开始初始化定时器和设置
2025-07-29 09:28:39 [INFO] 定时器和设置初始化完成
2025-07-29 09:28:39 [INFO] 开始VSTO插件启动流程
2025-07-29 09:28:39 [INFO] TopMostForm窗体加载完成
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:28:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:28:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:28:40 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 4656738
2025-07-29 09:28:40 [INFO] 系统事件监控已启动
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:28:40 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 09:28:40 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 09:28:40 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 09:28:40 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 13305530
2025-07-29 09:28:40 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 09:28:40 [INFO] VSTO插件启动流程完成
2025-07-29 09:28:40 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开事件触发
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:28:40 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:28:40 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开处理完成
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:28:40 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:28:40 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:28:40 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:28:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:28:40 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 09:28:40 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 09:28:40 [INFO] 网络授权信息已更新并触发回调
2025-07-29 09:28:40 [INFO] 网络授权信息已从 Network 更新
2025-07-29 09:28:40 [INFO] 授权版本: 1.0
2025-07-29 09:28:40 [INFO] 颁发者: ExtensionsTools
2025-07-29 09:28:40 [INFO] 用户数量: 2
2025-07-29 09:28:40 [INFO] 分组权限数量: 2
2025-07-29 09:28:40 [WARN] 配置文件中未找到用户组信息
2025-07-29 09:28:40 [INFO] 已重新设置用户组: []
2025-07-29 09:28:40 [INFO] 用户组信息已重新设置
2025-07-29 09:28:40 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 09:28:40 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:28:40 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 09:28:40 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 09:28:40 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:28:40 [DEBUG] 本地权限缓存已清空
2025-07-29 09:28:40 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 09:28:40 [INFO] 所有权限检查完成
2025-07-29 09:28:40 [DEBUG] 权限重新检查完成
2025-07-29 09:28:40 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:40 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:40 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:40 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:40 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:40 [INFO] UI界面权限状态已更新
2025-07-29 09:28:40 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:28:40 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:28:40 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 09:28:40 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 09:28:40 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 09:28:40 [INFO] 开始刷新Ribbon控件标题
2025-07-29 09:28:40 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 09:28:40 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 09:28:40 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:28:40 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:28:40 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:40 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:28:40 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:28:40 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:28:40 [INFO] 控件标题更正完成
2025-07-29 09:28:40 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 09:28:40 [INFO] Ribbon控件标题刷新完成
2025-07-29 09:28:40 [INFO] 控件标题刷新完成
2025-07-29 09:28:40 [DEBUG] Ribbon控件标题已刷新
2025-07-29 09:28:40 [INFO] 开始刷新控件标题
2025-07-29 09:28:40 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:28:40 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:28:40 [DEBUG] 控件标题刷新完成
2025-07-29 09:28:40 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:28:40 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:28:40 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:28:40 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:40 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:28:40 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:40 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:28:40 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:28:40 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:28:40 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:28:40 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:28:40 [INFO] 控件标题更正完成
2025-07-29 09:28:40 [INFO] 控件标题刷新完成
2025-07-29 09:28:40 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 09:28:40 [INFO] 开始刷新授权状态
2025-07-29 09:28:40 [DEBUG] 开始初始化授权验证
2025-07-29 09:28:40 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:28:40 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:28:40 [INFO] 开始初始化UI权限管理
2025-07-29 09:28:40 [DEBUG] [实例ID: 8ec9dfc7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:28:40 [DEBUG] 🔍 [实例ID: 8ec9dfc7] 字典引用一致性检查:
2025-07-29 09:28:40 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:28:40 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:28:40 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:28:40 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:28:40 [DEBUG] 控件权限管理器初始化完成 [实例ID: 8ec9dfc7]
2025-07-29 09:28:40 [DEBUG] 开始注册控件权限映射
2025-07-29 09:28:40 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:28:40 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:28:40 [INFO] 开始初始化权限验证
2025-07-29 09:28:40 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:28:40 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:28:41 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:28:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:28:41 [DEBUG] 开始重置 209 个命令栏
2025-07-29 09:28:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:28:41 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 09:28:41 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:28:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:28:41 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:28:41 [INFO] 所有权限检查完成
2025-07-29 09:28:41 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:28:41 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:41 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:28:41 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:28:41 [INFO] 权限验证初始化完成
2025-07-29 09:28:41 [INFO] UI权限管理初始化完成
2025-07-29 09:28:41 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:28:41 [INFO] 开始刷新控件标题
2025-07-29 09:28:41 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:28:41 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:41 [DEBUG] 控件标题刷新完成
2025-07-29 09:28:41 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:28:41 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:28:41 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:28:41 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:28:41 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:28:41 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:41 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:28:41 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:28:41 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:28:41 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:28:41 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:28:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:28:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:28:41 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:28:41 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:28:41 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:28:41 [INFO] 控件标题更正完成
2025-07-29 09:28:41 [INFO] 控件标题刷新完成
2025-07-29 09:28:41 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:28:41 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:28:41 [DEBUG] 授权验证初始化完成
2025-07-29 09:28:41 [INFO] 授权状态刷新完成
2025-07-29 09:28:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:28:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:28:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:28:41 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 09:28:41 [DEBUG] 重置命令栏: cell
2025-07-29 09:28:41 [DEBUG] 重置命令栏: column
2025-07-29 09:28:41 [DEBUG] 重置命令栏: row
2025-07-29 09:28:41 [DEBUG] 重置命令栏: cell
2025-07-29 09:28:41 [DEBUG] 重置命令栏: column
2025-07-29 09:28:41 [DEBUG] 重置命令栏: row
2025-07-29 09:28:41 [DEBUG] 重置命令栏: row
2025-07-29 09:28:41 [DEBUG] 重置命令栏: column
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:41 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:41 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:41 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:42 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 09:28:43 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:43 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:43 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:43 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:28:43 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:28:43 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:43 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:28:43 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:28:43 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:28:43 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:28:44 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:44 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:44 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:44 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:45 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:45 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:45 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:45 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:46 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:47 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:47 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:47 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:48 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:48 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:48 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:48 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:49 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:50 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:50 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:50 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:50 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:51 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:51 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:28:51 [DEBUG] 授权控制器已初始化
2025-07-29 09:28:51 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:28:51 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:28:52 [DEBUG] 已重置工作表标签菜单
2025-07-29 09:28:52 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:49:58 [INFO] 会话切换事件: SessionLock
2025-07-29 09:49:59 [INFO] 会话切换事件: RemoteDisconnect
2025-07-29 09:50:40 [INFO] 会话切换事件: RemoteConnect
2025-07-29 09:50:41 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:50:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:50:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:50:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:50:42 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:50:45 [INFO] 会话切换事件: SessionUnlock
2025-07-29 09:50:45 [INFO] 显示设置已变更
2025-07-29 09:50:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:50:47 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:50:47 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:50:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:50:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:50:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:50:47 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:50:47 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:50:47 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:50:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:50:47 [INFO] 显示设置变更后TopForm关系已重建
2025-07-29 09:50:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:50:47 [INFO] 会话恢复后TopForm关系已重建
2025-07-29 09:51:09 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 09:51:10 [INFO] 配置文件实例已在加载时初始化
2025-07-29 09:51:10 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 09:51:10 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 09:51:10 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 09:51:10 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 09:51:10 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:51:10 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 09:51:10 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 09:51:10 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 09:51:10 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:51:10 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 09:51:10 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 09:51:10 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 09:51:10 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:51:10 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 09:51:10 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 09:51:10 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 09:51:10 [INFO] 成功初始化Excel应用程序实例
2025-07-29 09:51:10 [INFO] 自动备份路径未配置
2025-07-29 09:51:10 [DEBUG] 开始初始化授权控制器
2025-07-29 09:51:10 [DEBUG] 授权系统初始化完成，耗时: 432ms
2025-07-29 09:51:10 [DEBUG] 开始初始化授权验证
2025-07-29 09:51:10 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 09:51:10 [DEBUG] 权限管理器初始化成功
2025-07-29 09:51:10 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:51:10 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:51:10 [INFO] 开始初始化UI权限管理
2025-07-29 09:51:10 [DEBUG] [实例ID: ee7666f7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:51:10 [DEBUG] 🔍 [实例ID: ee7666f7] 字典引用一致性检查:
2025-07-29 09:51:10 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:51:10 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:51:10 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:51:10 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:51:10 [DEBUG] 控件权限管理器初始化完成 [实例ID: ee7666f7]
2025-07-29 09:51:10 [DEBUG] 开始注册控件权限映射
2025-07-29 09:51:10 [INFO] 开始初始化全局控件映射
2025-07-29 09:51:10 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 09:51:10 [DEBUG] 开始生成控件标题映射
2025-07-29 09:51:10 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:51:10 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:51:10 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:51:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:51:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:51:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:10 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:51:10 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 09:51:10 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 09:51:10 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 09:51:10 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 09:51:10 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:51:10 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 09:51:10 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 09:51:10 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 09:51:10 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 09:51:10 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 09:51:10 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 09:51:10 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 09:51:10 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 09:51:10 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 09:51:10 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 09:51:10 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 09:51:10 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 09:51:10 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 09:51:10 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 09:51:10 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 09:51:10 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 09:51:10 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 09:51:10 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 09:51:10 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 09:51:10 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 09:51:10 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 09:51:10 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 09:51:10 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 09:51:10 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 09:51:10 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 09:51:10 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 09:51:10 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 09:51:10 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 09:51:10 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 09:51:10 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 09:51:10 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 09:51:10 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 09:51:10 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 09:51:10 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 09:51:10 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 09:51:10 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 09:51:10 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 09:51:10 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 09:51:11 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 09:51:11 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 09:51:11 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 09:51:11 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 09:51:11 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 09:51:11 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 09:51:11 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 09:51:11 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 09:51:11 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 09:51:11 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 09:51:11 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 09:51:11 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 09:51:11 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 09:51:11 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 09:51:11 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 09:51:11 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 09:51:11 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 09:51:11 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 09:51:11 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 09:51:11 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 09:51:11 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 09:51:11 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 09:51:11 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 09:51:11 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 09:51:11 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 09:51:11 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 09:51:11 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 09:51:11 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 09:51:11 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 09:51:11 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 09:51:11 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 09:51:11 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 09:51:11 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 09:51:11 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 09:51:11 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 09:51:11 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 09:51:11 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 09:51:11 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 09:51:11 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 09:51:11 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 09:51:11 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 09:51:11 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 09:51:11 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 09:51:11 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 09:51:11 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 09:51:11 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 09:51:11 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 09:51:11 [DEBUG] 开始生成控件权限映射
2025-07-29 09:51:11 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:51:11 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:51:11 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:51:11 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:51:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:51:11 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:51:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:51:11 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:51:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:51:11 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:51:11 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:51:11 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:51:11 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 09:51:11 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 09:51:11 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 09:51:11 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 09:51:11 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 09:51:11 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:51:11 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:51:11 [INFO] 开始初始化权限验证
2025-07-29 09:51:11 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:51:11 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:51:11 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 09:51:11 [INFO] 启动网络授权信息获取任务
2025-07-29 09:51:11 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 09:51:11 [INFO] 所有权限检查完成
2025-07-29 09:51:11 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:51:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:11 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:11 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:51:11 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:51:11 [INFO] 权限验证初始化完成
2025-07-29 09:51:11 [INFO] UI权限管理初始化完成
2025-07-29 09:51:11 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:51:11 [INFO] 开始刷新控件标题
2025-07-29 09:51:11 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:51:11 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:51:11 [DEBUG] 控件标题刷新完成
2025-07-29 09:51:11 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:51:11 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:51:11 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:51:11 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:51:11 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:51:11 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:51:11 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:11 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:51:11 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:11 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:51:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:51:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:51:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:51:11 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:51:11 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:51:11 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:51:11 [INFO] 控件标题更正完成
2025-07-29 09:51:11 [INFO] 控件标题刷新完成
2025-07-29 09:51:11 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:51:11 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:51:11 [DEBUG] 授权验证初始化完成
2025-07-29 09:51:11 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 09:51:11 [INFO] 成功加载配置和授权信息
2025-07-29 09:51:11 [INFO] 开始初始化定时器和设置
2025-07-29 09:51:11 [INFO] 定时器和设置初始化完成
2025-07-29 09:51:11 [INFO] 开始VSTO插件启动流程
2025-07-29 09:51:11 [INFO] TopMostForm窗体加载完成
2025-07-29 09:51:11 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:51:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8786716
2025-07-29 09:51:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8786716)
2025-07-29 09:51:11 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8786716
2025-07-29 09:51:11 [INFO] 系统事件监控已启动
2025-07-29 09:51:11 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:51:12 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 09:51:12 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 09:51:12 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 09:51:12 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 5246702
2025-07-29 09:51:12 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 09:51:12 [INFO] VSTO插件启动流程完成
2025-07-29 09:51:12 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 09:51:12 [INFO] 网络授权信息已更新并触发回调
2025-07-29 09:51:12 [INFO] 网络授权信息已从 Network 更新
2025-07-29 09:51:12 [INFO] 授权版本: 1.0
2025-07-29 09:51:12 [INFO] 颁发者: ExtensionsTools
2025-07-29 09:51:12 [INFO] 用户数量: 2
2025-07-29 09:51:12 [INFO] 分组权限数量: 2
2025-07-29 09:51:12 [WARN] 配置文件中未找到用户组信息
2025-07-29 09:51:12 [INFO] 已重新设置用户组: []
2025-07-29 09:51:12 [INFO] 用户组信息已重新设置
2025-07-29 09:51:12 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 09:51:12 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:51:12 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 09:51:12 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 09:51:12 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:51:12 [DEBUG] 本地权限缓存已清空
2025-07-29 09:51:12 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 09:51:12 [INFO] 所有权限检查完成
2025-07-29 09:51:12 [DEBUG] 权限重新检查完成
2025-07-29 09:51:12 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:12 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:12 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:12 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:12 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:12 [INFO] UI界面权限状态已更新
2025-07-29 09:51:12 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:51:12 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:51:12 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 09:51:12 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 09:51:12 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 09:51:12 [INFO] 开始刷新Ribbon控件标题
2025-07-29 09:51:12 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 09:51:12 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 09:51:12 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:51:12 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:51:12 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:12 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:51:12 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:51:12 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:51:12 [INFO] 控件标题更正完成
2025-07-29 09:51:12 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 09:51:12 [INFO] Ribbon控件标题刷新完成
2025-07-29 09:51:12 [INFO] 控件标题刷新完成
2025-07-29 09:51:12 [DEBUG] Ribbon控件标题已刷新
2025-07-29 09:51:12 [INFO] 开始刷新控件标题
2025-07-29 09:51:12 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:51:12 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:51:12 [DEBUG] 控件标题刷新完成
2025-07-29 09:51:12 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:51:12 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:51:12 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:51:12 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:12 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:51:12 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:12 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:51:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:51:12 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:51:12 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:51:12 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:51:12 [INFO] 控件标题更正完成
2025-07-29 09:51:12 [INFO] 控件标题刷新完成
2025-07-29 09:51:12 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 09:51:13 [INFO] 开始刷新授权状态
2025-07-29 09:51:13 [DEBUG] 开始初始化授权验证
2025-07-29 09:51:13 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:51:13 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:51:13 [INFO] 开始初始化UI权限管理
2025-07-29 09:51:13 [DEBUG] [实例ID: 09a75feb] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:51:13 [DEBUG] 🔍 [实例ID: 09a75feb] 字典引用一致性检查:
2025-07-29 09:51:13 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:51:13 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:51:13 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:51:13 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:51:13 [DEBUG] 控件权限管理器初始化完成 [实例ID: 09a75feb]
2025-07-29 09:51:13 [DEBUG] 开始注册控件权限映射
2025-07-29 09:51:13 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:51:13 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:51:13 [INFO] 开始初始化权限验证
2025-07-29 09:51:13 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:51:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:51:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8786716
2025-07-29 09:51:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8786716)
2025-07-29 09:51:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:51:13 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:13 [DEBUG] 开始重置 208 个命令栏
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:13 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:51:13 [INFO] 所有权限检查完成
2025-07-29 09:51:13 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:13 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:13 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:51:13 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:13 [INFO] 权限验证初始化完成
2025-07-29 09:51:13 [INFO] UI权限管理初始化完成
2025-07-29 09:51:13 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:51:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:13 [INFO] 开始刷新控件标题
2025-07-29 09:51:13 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:51:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:14 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:51:14 [DEBUG] 控件标题刷新完成
2025-07-29 09:51:14 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:51:14 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:51:14 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:51:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:14 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:51:14 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:14 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:51:14 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:51:14 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:51:14 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:51:14 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:51:14 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:51:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:51:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:51:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:51:14 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:51:14 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:51:14 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:51:14 [INFO] 控件标题更正完成
2025-07-29 09:51:14 [INFO] 控件标题刷新完成
2025-07-29 09:51:14 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:51:14 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:51:14 [DEBUG] 授权验证初始化完成
2025-07-29 09:51:14 [INFO] 授权状态刷新完成
2025-07-29 09:51:14 [DEBUG] 重置命令栏: cell
2025-07-29 09:51:14 [DEBUG] 重置命令栏: column
2025-07-29 09:51:14 [DEBUG] 重置命令栏: row
2025-07-29 09:51:14 [DEBUG] 重置命令栏: cell
2025-07-29 09:51:14 [DEBUG] 重置命令栏: column
2025-07-29 09:51:14 [DEBUG] 重置命令栏: row
2025-07-29 09:51:14 [DEBUG] 重置命令栏: row
2025-07-29 09:51:14 [DEBUG] 重置命令栏: column
2025-07-29 09:51:14 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 09:51:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:15 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:15 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:51:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:51:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:51:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:51:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:51:16 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:51:16 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:16 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:16 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:16 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:17 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:17 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:18 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:19 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:20 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:20 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:20 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:20 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:21 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:21 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:21 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:21 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:22 [DEBUG] 授权控制器已初始化
2025-07-29 09:51:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:51:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:51:23 [DEBUG] 已重置工作表标签菜单
2025-07-29 09:51:23 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:51:23 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 09:51:23 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 09:51:23 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 09:51:23 [INFO] 窗体 '备份及发送' 显示完成，句柄: 11010948
2025-07-29 09:51:23 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 09:52:15 [INFO] 会话切换事件: SessionLock
2025-07-29 09:52:15 [INFO] 会话切换事件: RemoteDisconnect
2025-07-29 09:52:15 [INFO] 会话切换事件: SessionLock
2025-07-29 09:52:15 [INFO] 会话切换事件: RemoteDisconnect
2025-07-29 09:52:37 [INFO] 会话切换事件: RemoteConnect
2025-07-29 09:52:37 [INFO] 会话切换事件: RemoteConnect
2025-07-29 09:52:39 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:39 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:39 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:52:39 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:52:39 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:39 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:52:39 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8786716
2025-07-29 09:52:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8786716)
2025-07-29 09:52:40 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:40 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:52:42 [INFO] 会话切换事件: SessionUnlock
2025-07-29 09:52:42 [INFO] 会话切换事件: SessionUnlock
2025-07-29 09:52:42 [INFO] 显示设置已变更
2025-07-29 09:52:42 [INFO] 显示设置已变更
2025-07-29 09:52:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:43 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 09:52:43 [INFO] 显示设置变更后TopForm关系已重建
2025-07-29 09:52:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:43 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 09:52:43 [INFO] 会话恢复后TopForm关系已重建
2025-07-29 09:52:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8786716
2025-07-29 09:52:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8786716)
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:45 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:52:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4656738
2025-07-29 09:52:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4656738)
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:45 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:52:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:52:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4656738)
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:45 [INFO] 显示设置变更后TopForm关系已重建
2025-07-29 09:52:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:52:45 [INFO] 会话恢复后TopForm关系已重建
2025-07-29 09:56:21 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 09:56:21 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 09:56:22 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 09:56:22 [INFO] 窗体 '备份及发送' 显示完成，句柄: 6360506
2025-07-29 09:56:22 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 09:56:25 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-29 09:56:25 [INFO] 系统事件监控已停止
2025-07-29 09:56:25 [INFO] Excel窗口句柄监控已停止
2025-07-29 09:56:25 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-29 09:56:27 [INFO] 开始VSTO插件关闭流程
2025-07-29 09:56:27 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250729_095627.txt
2025-07-29 09:56:27 [INFO] VSTO插件关闭流程完成
2025-07-29 09:58:33 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 09:58:34 [INFO] 配置文件实例已在加载时初始化
2025-07-29 09:58:34 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 09:58:34 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 09:58:34 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 09:58:34 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 09:58:34 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:58:34 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 09:58:34 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 09:58:34 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:58:34 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 09:58:34 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 09:58:34 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:58:34 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 09:58:34 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 09:58:34 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 09:58:34 [INFO] 成功初始化Excel应用程序实例
2025-07-29 09:58:34 [INFO] 自动备份路径未配置
2025-07-29 09:58:34 [DEBUG] 开始初始化授权控制器
2025-07-29 09:58:34 [DEBUG] 授权系统初始化完成，耗时: 331ms
2025-07-29 09:58:34 [DEBUG] 开始初始化授权验证
2025-07-29 09:58:34 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 09:58:34 [DEBUG] 权限管理器初始化成功
2025-07-29 09:58:34 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:58:34 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:58:34 [INFO] 开始初始化UI权限管理
2025-07-29 09:58:34 [DEBUG] [实例ID: 32edb5b1] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:58:34 [DEBUG] 🔍 [实例ID: 32edb5b1] 字典引用一致性检查:
2025-07-29 09:58:34 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:58:34 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:58:34 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:58:34 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:58:34 [DEBUG] 控件权限管理器初始化完成 [实例ID: 32edb5b1]
2025-07-29 09:58:34 [DEBUG] 开始注册控件权限映射
2025-07-29 09:58:34 [INFO] 开始初始化全局控件映射
2025-07-29 09:58:34 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 09:58:34 [DEBUG] 开始生成控件标题映射
2025-07-29 09:58:34 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:58:34 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:58:34 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:58:34 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 09:58:34 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 09:58:34 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:58:34 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 09:58:34 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 09:58:34 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 09:58:34 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 09:58:34 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 09:58:34 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 09:58:34 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 09:58:34 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 09:58:34 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 09:58:34 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 09:58:34 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 09:58:34 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 09:58:34 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 09:58:34 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 09:58:34 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 09:58:34 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 09:58:34 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 09:58:34 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 09:58:34 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 09:58:34 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 09:58:34 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 09:58:34 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 09:58:34 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 09:58:34 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 09:58:34 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 09:58:34 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 09:58:34 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 09:58:34 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 09:58:34 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 09:58:34 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 09:58:34 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 09:58:34 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 09:58:34 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 09:58:34 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 09:58:34 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 09:58:34 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 09:58:34 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 09:58:34 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 09:58:34 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 09:58:34 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 09:58:34 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 09:58:34 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 09:58:34 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 09:58:34 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 09:58:34 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 09:58:34 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 09:58:34 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 09:58:34 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 09:58:34 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 09:58:34 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 09:58:34 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 09:58:34 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 09:58:34 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 09:58:34 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 09:58:34 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 09:58:34 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 09:58:34 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 09:58:34 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 09:58:34 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 09:58:34 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 09:58:34 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 09:58:34 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 09:58:34 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 09:58:34 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 09:58:34 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 09:58:34 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 09:58:34 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 09:58:34 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 09:58:34 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 09:58:34 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 09:58:34 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 09:58:34 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 09:58:34 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 09:58:34 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 09:58:34 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 09:58:34 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 09:58:34 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 09:58:34 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 09:58:34 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 09:58:34 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 09:58:34 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 09:58:34 [DEBUG] 开始生成控件权限映射
2025-07-29 09:58:34 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 09:58:34 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 09:58:34 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 09:58:34 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 09:58:34 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 09:58:34 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 09:58:34 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 09:58:34 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 09:58:34 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 09:58:34 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 09:58:34 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 09:58:34 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 09:58:34 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 09:58:34 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:58:34 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:58:34 [INFO] 开始初始化权限验证
2025-07-29 09:58:34 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:58:34 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:58:34 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 09:58:35 [INFO] 启动网络授权信息获取任务
2025-07-29 09:58:35 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 09:58:35 [INFO] 所有权限检查完成
2025-07-29 09:58:35 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:58:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:35 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:35 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:58:35 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:58:35 [INFO] 权限验证初始化完成
2025-07-29 09:58:35 [INFO] UI权限管理初始化完成
2025-07-29 09:58:35 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:58:35 [INFO] 开始刷新控件标题
2025-07-29 09:58:35 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:58:35 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:58:35 [DEBUG] 控件标题刷新完成
2025-07-29 09:58:35 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:58:35 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:58:35 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:58:35 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:58:35 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:58:35 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:58:35 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:35 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:58:35 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:35 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:58:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:58:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:58:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:58:35 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:58:35 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:58:35 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:58:35 [INFO] 控件标题更正完成
2025-07-29 09:58:35 [INFO] 控件标题刷新完成
2025-07-29 09:58:35 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:58:35 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:58:35 [DEBUG] 授权验证初始化完成
2025-07-29 09:58:35 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 09:58:35 [INFO] 成功加载配置和授权信息
2025-07-29 09:58:35 [INFO] 开始初始化定时器和设置
2025-07-29 09:58:35 [INFO] 定时器和设置初始化完成
2025-07-29 09:58:35 [INFO] 开始VSTO插件启动流程
2025-07-29 09:58:35 [INFO] TopMostForm窗体加载完成
2025-07-29 09:58:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:58:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6428056
2025-07-29 09:58:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6428056)
2025-07-29 09:58:35 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 6428056
2025-07-29 09:58:35 [INFO] 系统事件监控已启动
2025-07-29 09:58:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:58:35 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 09:58:35 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 09:58:35 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 09:58:35 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 87299298
2025-07-29 09:58:35 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 09:58:35 [INFO] VSTO插件启动流程完成
2025-07-29 09:58:36 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 09:58:36 [INFO] 网络授权信息已更新并触发回调
2025-07-29 09:58:36 [INFO] 网络授权信息已从 Network 更新
2025-07-29 09:58:36 [INFO] 授权版本: 1.0
2025-07-29 09:58:36 [INFO] 颁发者: ExtensionsTools
2025-07-29 09:58:36 [INFO] 用户数量: 2
2025-07-29 09:58:36 [INFO] 分组权限数量: 2
2025-07-29 09:58:36 [WARN] 配置文件中未找到用户组信息
2025-07-29 09:58:36 [INFO] 已重新设置用户组: []
2025-07-29 09:58:36 [INFO] 用户组信息已重新设置
2025-07-29 09:58:36 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 09:58:36 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:58:36 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 09:58:36 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 09:58:36 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 09:58:36 [DEBUG] 本地权限缓存已清空
2025-07-29 09:58:36 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 09:58:36 [INFO] 所有权限检查完成
2025-07-29 09:58:36 [DEBUG] 权限重新检查完成
2025-07-29 09:58:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:36 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:36 [INFO] UI界面权限状态已更新
2025-07-29 09:58:36 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:58:36 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:58:36 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 09:58:36 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 09:58:36 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 09:58:36 [INFO] 开始刷新Ribbon控件标题
2025-07-29 09:58:36 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 09:58:36 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 09:58:36 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:58:36 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:58:36 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:36 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:58:36 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:58:36 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:58:36 [INFO] 控件标题更正完成
2025-07-29 09:58:36 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 09:58:36 [INFO] Ribbon控件标题刷新完成
2025-07-29 09:58:36 [INFO] 控件标题刷新完成
2025-07-29 09:58:36 [DEBUG] Ribbon控件标题已刷新
2025-07-29 09:58:36 [INFO] 开始刷新控件标题
2025-07-29 09:58:36 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:58:36 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:58:36 [DEBUG] 控件标题刷新完成
2025-07-29 09:58:36 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:58:36 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:58:36 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:58:36 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:36 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:58:36 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:36 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:58:36 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:58:36 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:58:36 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:58:36 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:58:36 [INFO] 控件标题更正完成
2025-07-29 09:58:36 [INFO] 控件标题刷新完成
2025-07-29 09:58:36 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 09:58:36 [INFO] 开始刷新授权状态
2025-07-29 09:58:36 [DEBUG] 开始初始化授权验证
2025-07-29 09:58:36 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 09:58:36 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 09:58:36 [INFO] 开始初始化UI权限管理
2025-07-29 09:58:36 [DEBUG] [实例ID: 3bbaab1e] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 09:58:36 [DEBUG] 🔍 [实例ID: 3bbaab1e] 字典引用一致性检查:
2025-07-29 09:58:36 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 09:58:36 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 09:58:36 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 09:58:36 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 09:58:36 [DEBUG] 控件权限管理器初始化完成 [实例ID: 3bbaab1e]
2025-07-29 09:58:36 [DEBUG] 开始注册控件权限映射
2025-07-29 09:58:36 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 09:58:36 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 09:58:36 [INFO] 开始初始化权限验证
2025-07-29 09:58:36 [DEBUG] 设置默认UI可见性为false
2025-07-29 09:58:36 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:58:36 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6428056
2025-07-29 09:58:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6428056)
2025-07-29 09:58:36 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 09:58:36 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 09:58:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:37 [DEBUG] 开始重置 208 个命令栏
2025-07-29 09:58:37 [DEBUG] 开始检查所有需要的权限
2025-07-29 09:58:37 [INFO] 所有权限检查完成
2025-07-29 09:58:37 [DEBUG] 应用权限状态到UI控件
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:37 [DEBUG] 启动后台权限刷新任务
2025-07-29 09:58:37 [DEBUG] 启动延迟权限刷新任务
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:37 [INFO] 权限验证初始化完成
2025-07-29 09:58:37 [INFO] UI权限管理初始化完成
2025-07-29 09:58:37 [INFO] 收到权限管理器初始化完成通知
2025-07-29 09:58:37 [INFO] 开始刷新控件标题
2025-07-29 09:58:37 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 09:58:37 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 09:58:37 [DEBUG] 控件标题刷新完成
2025-07-29 09:58:37 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 09:58:37 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 09:58:37 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 09:58:37 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:37 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:37 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:37 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 09:58:37 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:37 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:37 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 09:58:37 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 09:58:37 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:37 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 09:58:37 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:37 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 09:58:37 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:37 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 09:58:37 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 09:58:37 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 09:58:37 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 09:58:37 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 09:58:37 [INFO] 控件标题更正完成
2025-07-29 09:58:37 [INFO] 控件标题刷新完成
2025-07-29 09:58:37 [INFO] 权限管理器初始化完成处理结束
2025-07-29 09:58:37 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 09:58:37 [DEBUG] 授权验证初始化完成
2025-07-29 09:58:37 [INFO] 授权状态刷新完成
2025-07-29 09:58:37 [DEBUG] 重置命令栏: cell
2025-07-29 09:58:37 [DEBUG] 重置命令栏: column
2025-07-29 09:58:37 [DEBUG] 重置命令栏: row
2025-07-29 09:58:37 [DEBUG] 重置命令栏: cell
2025-07-29 09:58:37 [DEBUG] 重置命令栏: column
2025-07-29 09:58:37 [DEBUG] 重置命令栏: row
2025-07-29 09:58:37 [DEBUG] 重置命令栏: row
2025-07-29 09:58:37 [DEBUG] 重置命令栏: column
2025-07-29 09:58:37 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 09:58:38 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:38 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:38 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:38 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:39 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 09:58:39 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 09:58:39 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:39 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 09:58:39 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 09:58:39 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 09:58:39 [DEBUG] 已应用权限状态到UI控件
2025-07-29 09:58:39 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:39 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:39 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:39 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:39 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:39 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:39 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:40 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:40 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:40 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:40 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:40 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:41 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:41 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:41 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:41 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:41 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:41 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:41 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:41 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:42 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:42 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:42 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:42 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:42 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:58:42 [DEBUG] 授权控制器已初始化
2025-07-29 09:58:42 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 09:58:42 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 09:58:42 [DEBUG] 已重置工作表标签菜单
2025-07-29 09:58:42 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 09:59:14 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 09:59:14 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 09:59:15 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 09:59:15 [INFO] 窗体 '备份及发送' 显示完成，句柄: 15860698
2025-07-29 09:59:15 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 09:59:21 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开事件触发
2025-07-29 09:59:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:59:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6428056, 新父窗口: 5511014
2025-07-29 09:59:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5511014)
2025-07-29 09:59:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:59:21 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开处理完成
2025-07-29 09:59:22 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:59:22 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5511014)
2025-07-29 09:59:22 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:59:22 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 09:59:22 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 09:59:22 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5511014)
2025-07-29 09:59:22 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 09:59:22 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 09:59:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:59:22 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 09:59:22 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 09:59:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:59:22 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 09:59:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 09:59:22 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 09:59:22 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 09:59:25 [WARN] 检测到Excel窗口句柄变化: 6428056 -> 5511014
2025-07-29 09:59:25 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5511014)
2025-07-29 09:59:25 [INFO] 成功保存到临时文件夹：E:\工作临时文件\2025-07-29\20250729-095925骨头点黑点-20241122(2).xlsx
2025-07-29 09:59:28 [INFO] 已打开目录：E:\工作临时文件\2025-07-29
2025-07-29 10:00:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:05 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 5511014, 新父窗口: 27334672
2025-07-29 10:00:05 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 27334672)
2025-07-29 10:00:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:05 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:06 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:06 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:06 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:06 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:06 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:06 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:06 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 27334672, 新父窗口: 5511014
2025-07-29 10:00:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 5511014)
2025-07-29 10:00:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:08 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 10:00:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:08 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 5511014)
2025-07-29 10:00:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:08 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:08 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:08 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:08 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:09 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-29 10:00:09 [INFO] 系统事件监控已停止
2025-07-29 10:00:09 [INFO] Excel窗口句柄监控已停止
2025-07-29 10:00:09 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-29 10:00:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 27334672
2025-07-29 10:00:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 27334672)
2025-07-29 10:00:09 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 27334672
2025-07-29 10:00:09 [INFO] 系统事件监控已启动
2025-07-29 10:00:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:09 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-29 10:00:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:09 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 27334672)
2025-07-29 10:00:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:09 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:09 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:09 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:09 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:10 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 27334672
2025-07-29 10:00:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 27334672)
2025-07-29 10:00:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 10:00:11 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 10:00:11 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:11 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 27334672
2025-07-29 10:00:11 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 27334672)
2025-07-29 10:00:11 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 10:00:11 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 10:00:20 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开事件触发
2025-07-29 10:00:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 27334672, 新父窗口: 10552700
2025-07-29 10:00:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10552700)
2025-07-29 10:00:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:20 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开处理完成
2025-07-29 10:00:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10552700)
2025-07-29 10:00:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:21 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-29 10:00:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:00:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10552700)
2025-07-29 10:00:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:00:21 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:00:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:21 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:00:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:21 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:00:21 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:00:21 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 10:00:23 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:00:23 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:00:23 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:00:23 [INFO] 窗体 '备份及发送' 显示完成，句柄: 12655198
2025-07-29 10:00:23 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 10:00:24 [WARN] 检测到Excel窗口句柄变化: 27334672 -> 10552700
2025-07-29 10:00:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10552700)
2025-07-29 10:00:27 [INFO] 成功保存到临时文件夹：E:\工作临时文件\2025-07-29\20250729-1000262025年无线网5GA工程管控表0727.xlsx
2025-07-29 10:00:36 [INFO] 成功另存到临时文件夹：E:\工作临时文件\2025-07-29\20250729-1000352025年无线网5GA工程管控表0727.xlsx
2025-07-29 10:06:41 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 10:06:42 [INFO] 配置文件实例已在加载时初始化
2025-07-29 10:06:42 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 10:06:42 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 10:06:42 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 10:06:42 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 10:06:42 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:06:42 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 10:06:42 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 10:06:42 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 10:06:42 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:06:42 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 10:06:42 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 10:06:42 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 10:06:42 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:06:42 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 10:06:42 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 10:06:42 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 10:06:42 [INFO] 成功初始化Excel应用程序实例
2025-07-29 10:06:42 [INFO] 自动备份路径未配置
2025-07-29 10:06:42 [DEBUG] 开始初始化授权控制器
2025-07-29 10:06:42 [DEBUG] 授权系统初始化完成，耗时: 386ms
2025-07-29 10:06:42 [DEBUG] 开始初始化授权验证
2025-07-29 10:06:42 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 10:06:42 [DEBUG] 权限管理器初始化成功
2025-07-29 10:06:42 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:06:42 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:06:42 [INFO] 开始初始化UI权限管理
2025-07-29 10:06:42 [DEBUG] [实例ID: ae3056d1] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:06:42 [DEBUG] 🔍 [实例ID: ae3056d1] 字典引用一致性检查:
2025-07-29 10:06:42 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:06:42 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:06:42 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:06:42 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:06:42 [DEBUG] 控件权限管理器初始化完成 [实例ID: ae3056d1]
2025-07-29 10:06:42 [DEBUG] 开始注册控件权限映射
2025-07-29 10:06:42 [INFO] 开始初始化全局控件映射
2025-07-29 10:06:42 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 10:06:42 [DEBUG] 开始生成控件标题映射
2025-07-29 10:06:42 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:06:42 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:06:42 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:06:42 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:06:42 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:06:42 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:06:42 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 10:06:42 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 10:06:42 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 10:06:42 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 10:06:42 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:06:42 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 10:06:42 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 10:06:42 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 10:06:42 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 10:06:42 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 10:06:42 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 10:06:42 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 10:06:42 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 10:06:42 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 10:06:42 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 10:06:42 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 10:06:42 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 10:06:42 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 10:06:42 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 10:06:42 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 10:06:42 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 10:06:42 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 10:06:42 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 10:06:42 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 10:06:42 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 10:06:42 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 10:06:42 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 10:06:42 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 10:06:42 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 10:06:42 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 10:06:42 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 10:06:42 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 10:06:42 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 10:06:42 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 10:06:42 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 10:06:42 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 10:06:42 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 10:06:42 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 10:06:42 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 10:06:42 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 10:06:42 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 10:06:42 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 10:06:42 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 10:06:42 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 10:06:42 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 10:06:42 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 10:06:42 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 10:06:42 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 10:06:42 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 10:06:42 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 10:06:42 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 10:06:42 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 10:06:42 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 10:06:42 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 10:06:42 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 10:06:42 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 10:06:42 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 10:06:42 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 10:06:42 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 10:06:42 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 10:06:42 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 10:06:42 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 10:06:42 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 10:06:42 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 10:06:42 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 10:06:42 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 10:06:42 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 10:06:42 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 10:06:42 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 10:06:42 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 10:06:42 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 10:06:42 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 10:06:42 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 10:06:42 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 10:06:42 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 10:06:42 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 10:06:42 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 10:06:42 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 10:06:42 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 10:06:42 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 10:06:42 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 10:06:42 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 10:06:42 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 10:06:42 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 10:06:42 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 10:06:42 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 10:06:42 [DEBUG] 开始生成控件权限映射
2025-07-29 10:06:42 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:06:42 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:06:42 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:42 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:06:43 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:06:43 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:06:43 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:06:43 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:06:43 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:06:43 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:06:43 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:06:43 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:06:43 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:06:43 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 10:06:43 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 10:06:43 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 10:06:43 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 10:06:43 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 10:06:43 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:06:43 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:06:43 [INFO] 开始初始化权限验证
2025-07-29 10:06:43 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:06:43 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:06:43 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 10:06:43 [INFO] 启动网络授权信息获取任务
2025-07-29 10:06:43 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 10:06:43 [INFO] 所有权限检查完成
2025-07-29 10:06:43 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:06:43 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:43 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:43 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:43 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:43 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:43 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:06:43 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:06:43 [INFO] 权限验证初始化完成
2025-07-29 10:06:43 [INFO] UI权限管理初始化完成
2025-07-29 10:06:43 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:06:43 [INFO] 开始刷新控件标题
2025-07-29 10:06:43 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:06:43 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:06:43 [DEBUG] 控件标题刷新完成
2025-07-29 10:06:43 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:06:43 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:06:43 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:06:43 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:06:43 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:06:43 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:43 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:06:43 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:43 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:06:43 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:43 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:06:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:06:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:06:43 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:06:43 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:06:43 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:06:43 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:06:43 [INFO] 控件标题更正完成
2025-07-29 10:06:43 [INFO] 控件标题刷新完成
2025-07-29 10:06:43 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:06:43 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:06:43 [DEBUG] 授权验证初始化完成
2025-07-29 10:06:43 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 10:06:43 [INFO] 成功加载配置和授权信息
2025-07-29 10:06:43 [INFO] 开始初始化定时器和设置
2025-07-29 10:06:43 [INFO] 定时器和设置初始化完成
2025-07-29 10:06:43 [INFO] 开始VSTO插件启动流程
2025-07-29 10:06:43 [INFO] TopMostForm窗体加载完成
2025-07-29 10:06:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:06:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6884750
2025-07-29 10:06:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6884750)
2025-07-29 10:06:43 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 6884750
2025-07-29 10:06:43 [INFO] 系统事件监控已启动
2025-07-29 10:06:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:06:43 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 10:06:43 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 10:06:43 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 10:06:43 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 8325634
2025-07-29 10:06:43 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 10:06:43 [INFO] VSTO插件启动流程完成
2025-07-29 10:06:44 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 10:06:44 [INFO] 网络授权信息已更新并触发回调
2025-07-29 10:06:44 [INFO] 网络授权信息已从 Network 更新
2025-07-29 10:06:44 [INFO] 授权版本: 1.0
2025-07-29 10:06:44 [INFO] 颁发者: ExtensionsTools
2025-07-29 10:06:44 [INFO] 用户数量: 2
2025-07-29 10:06:44 [INFO] 分组权限数量: 2
2025-07-29 10:06:44 [WARN] 配置文件中未找到用户组信息
2025-07-29 10:06:44 [INFO] 已重新设置用户组: []
2025-07-29 10:06:44 [INFO] 用户组信息已重新设置
2025-07-29 10:06:44 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 10:06:44 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:06:44 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 10:06:44 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 10:06:44 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:06:44 [DEBUG] 本地权限缓存已清空
2025-07-29 10:06:44 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 10:06:44 [INFO] 所有权限检查完成
2025-07-29 10:06:44 [DEBUG] 权限重新检查完成
2025-07-29 10:06:44 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:44 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:44 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:44 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:44 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:44 [INFO] UI界面权限状态已更新
2025-07-29 10:06:44 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:06:44 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:06:44 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 10:06:44 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 10:06:44 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 10:06:44 [INFO] 开始刷新Ribbon控件标题
2025-07-29 10:06:44 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 10:06:44 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 10:06:44 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:06:44 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:06:44 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:44 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:06:44 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:06:44 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:06:44 [INFO] 控件标题更正完成
2025-07-29 10:06:44 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 10:06:44 [INFO] Ribbon控件标题刷新完成
2025-07-29 10:06:44 [INFO] 控件标题刷新完成
2025-07-29 10:06:44 [DEBUG] Ribbon控件标题已刷新
2025-07-29 10:06:44 [INFO] 开始刷新控件标题
2025-07-29 10:06:44 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:06:44 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:06:44 [DEBUG] 控件标题刷新完成
2025-07-29 10:06:44 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:06:44 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:06:44 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:06:44 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:44 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:06:44 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:44 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:06:44 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:06:44 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:06:44 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:06:44 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:06:44 [INFO] 控件标题更正完成
2025-07-29 10:06:44 [INFO] 控件标题刷新完成
2025-07-29 10:06:44 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 10:06:44 [INFO] 开始刷新授权状态
2025-07-29 10:06:44 [DEBUG] 开始初始化授权验证
2025-07-29 10:06:44 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:06:44 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:06:44 [INFO] 开始初始化UI权限管理
2025-07-29 10:06:44 [DEBUG] [实例ID: ee0989e1] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:06:44 [DEBUG] 🔍 [实例ID: ee0989e1] 字典引用一致性检查:
2025-07-29 10:06:44 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:06:44 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:06:44 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:06:44 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:06:44 [DEBUG] 控件权限管理器初始化完成 [实例ID: ee0989e1]
2025-07-29 10:06:44 [DEBUG] 开始注册控件权限映射
2025-07-29 10:06:44 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:06:44 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:06:44 [INFO] 开始初始化权限验证
2025-07-29 10:06:44 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:06:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:06:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 6884750
2025-07-29 10:06:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6884750)
2025-07-29 10:06:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 10:06:45 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:45 [DEBUG] 开始重置 209 个命令栏
2025-07-29 10:06:45 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:06:45 [INFO] 所有权限检查完成
2025-07-29 10:06:45 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:45 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:06:45 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:45 [INFO] 权限验证初始化完成
2025-07-29 10:06:45 [INFO] UI权限管理初始化完成
2025-07-29 10:06:45 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:06:45 [INFO] 开始刷新控件标题
2025-07-29 10:06:45 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:06:45 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:06:45 [DEBUG] 控件标题刷新完成
2025-07-29 10:06:45 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:45 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:45 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:06:45 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:45 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:06:45 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:45 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:06:45 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:06:45 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:06:45 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:06:45 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:06:45 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:06:45 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:06:45 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:06:45 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:06:45 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:06:45 [INFO] 控件标题更正完成
2025-07-29 10:06:45 [INFO] 控件标题刷新完成
2025-07-29 10:06:45 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:45 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:06:45 [DEBUG] 授权验证初始化完成
2025-07-29 10:06:45 [INFO] 授权状态刷新完成
2025-07-29 10:06:45 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:45 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:45 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:45 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:45 [DEBUG] 重置命令栏: cell
2025-07-29 10:06:45 [DEBUG] 重置命令栏: column
2025-07-29 10:06:45 [DEBUG] 重置命令栏: row
2025-07-29 10:06:45 [DEBUG] 重置命令栏: cell
2025-07-29 10:06:45 [DEBUG] 重置命令栏: column
2025-07-29 10:06:45 [DEBUG] 重置命令栏: row
2025-07-29 10:06:45 [DEBUG] 重置命令栏: row
2025-07-29 10:06:45 [DEBUG] 重置命令栏: column
2025-07-29 10:06:46 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 10:06:47 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开事件触发
2025-07-29 10:06:47 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:06:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6884750, 新父窗口: 6558302
2025-07-29 10:06:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6558302)
2025-07-29 10:06:47 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:06:47 [INFO] App_WorkbookOpen: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 打开处理完成
2025-07-29 10:06:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:06:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6558302)
2025-07-29 10:06:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:06:48 [INFO] App_WorkbookActivate: 工作簿 '2025年无线网5GA工程管控表0727.xlsx' 激活处理完成
2025-07-29 10:06:48 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:06:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6558302)
2025-07-29 10:06:48 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:06:48 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:06:48 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:06:48 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:06:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:48 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:06:48 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:06:48 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:06:48 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:06:48 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:48 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:48 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:06:48 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:06:48 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:06:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:06:48 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:06:48 [WARN] 检测到Excel窗口句柄变化: 6884750 -> 6558302
2025-07-29 10:06:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6558302)
2025-07-29 10:06:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:06:48 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:06:48 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 10:06:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:49 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:49 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:49 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:49 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:50 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:50 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:50 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:50 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:50 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:50 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:50 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:51 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:51 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:51 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:51 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:52 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:52 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:52 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:52 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:52 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:52 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:52 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:52 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:53 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:53 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:53 [DEBUG] 授权控制器已初始化
2025-07-29 10:06:53 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:06:53 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:06:53 [DEBUG] 已重置工作表标签菜单
2025-07-29 10:06:53 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:06:58 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:06:58 [INFO] OpenForm: 准备打开窗体 '文件操作'，位置: Center，单实例: True
2025-07-29 10:06:58 [INFO] 开始显示窗体 '文件操作'，位置模式: Center
2025-07-29 10:06:59 [INFO] 窗体 '文件操作' 以TopMostForm为父窗体显示
2025-07-29 10:06:59 [INFO] 窗体 '文件操作' 显示完成，句柄: 18552228
2025-07-29 10:06:59 [INFO] OpenForm: 窗体 '文件操作' 打开成功
2025-07-29 10:06:59 [INFO] OpenForm: 准备打开窗体 '文件操作'，位置: Center，单实例: True
2025-07-29 10:06:59 [INFO] OpenForm: 窗体 '文件操作' 已存在，激活现有实例
2025-07-29 10:07:01 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:07:01 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:07:01 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:07:01 [INFO] 窗体 '备份及发送' 显示完成，句柄: 5510680
2025-07-29 10:07:01 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 10:07:05 [INFO] 文件已保存到临时文件夹，当前操作文件变为：E:\工作临时文件\2025-07-29\2025年无线网5GA工程管控表0727.xlsx
2025-07-29 10:07:05 [INFO] 成功保存到临时文件夹：E:\工作临时文件\2025-07-29\2025年无线网5GA工程管控表0727.xlsx
2025-07-29 10:07:21 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开事件触发
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 6558302, 新父窗口: 1187094
2025-07-29 10:07:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1187094)
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:21 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开处理完成
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1187094)
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:21 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:21 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1187094)
2025-07-29 10:07:21 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:21 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:07:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:21 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:07:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:22 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:22 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:22 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 10:07:23 [WARN] 检测到Excel窗口句柄变化: 6558302 -> 1187094
2025-07-29 10:07:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1187094)
2025-07-29 10:07:38 [INFO] App_WorkbookOpen: 工作簿 '交付模板_1753174053976.xlsx' 打开事件触发
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:38 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 1187094, 新父窗口: 9706566
2025-07-29 10:07:38 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 9706566)
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:38 [INFO] App_WorkbookOpen: 工作簿 '交付模板_1753174053976.xlsx' 打开处理完成
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:38 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 9706566)
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:38 [INFO] App_WorkbookActivate: 工作簿 '交付模板_1753174053976.xlsx' 激活处理完成
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:07:38 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 9706566)
2025-07-29 10:07:38 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:07:38 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:07:38 [WARN] 检测到Excel窗口句柄变化: 1187094 -> 9706566
2025-07-29 10:07:38 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 9706566)
2025-07-29 10:07:39 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:39 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:39 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:07:39 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:39 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:39 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:07:39 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:07:39 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 10:07:42 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:07:42 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:07:42 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:07:42 [INFO] 窗体 '备份及发送' 显示完成，句柄: 22353094
2025-07-29 10:07:42 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 10:07:44 [INFO] 文件副本已保存到临时文件夹：E:\工作临时文件\2025-07-29\交付模板_1753174053976.xlsx，当前操作文件保持不变
2025-07-29 10:07:44 [INFO] 成功另存副本到临时文件夹：E:\工作临时文件\2025-07-29\交付模板_1753174053976.xlsx
2025-07-29 10:07:59 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-29 10:07:59 [INFO] 系统事件监控已停止
2025-07-29 10:07:59 [INFO] Excel窗口句柄监控已停止
2025-07-29 10:08:00 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-29 10:08:00 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:08:00 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1187094
2025-07-29 10:08:00 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1187094)
2025-07-29 10:08:00 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 1187094
2025-07-29 10:08:00 [INFO] 系统事件监控已启动
2025-07-29 10:08:00 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:08:00 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 10:08:00 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:08:00 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1187094)
2025-07-29 10:08:00 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:08:00 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:08:00 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:08:00 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:08:00 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:08:00 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:08:00 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:11:13 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 10:11:13 [INFO] 配置文件实例已在加载时初始化
2025-07-29 10:11:13 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 10:11:13 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 10:11:13 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 10:11:13 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 10:11:13 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:11:13 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 10:11:13 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 10:11:13 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 10:11:13 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:11:13 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 10:11:13 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 10:11:13 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 10:11:13 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:11:13 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 10:11:13 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 10:11:13 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 10:11:13 [INFO] 成功初始化Excel应用程序实例
2025-07-29 10:11:13 [INFO] 自动备份路径未配置
2025-07-29 10:11:13 [DEBUG] 开始初始化授权控制器
2025-07-29 10:11:13 [DEBUG] 授权系统初始化完成，耗时: 365ms
2025-07-29 10:11:13 [DEBUG] 开始初始化授权验证
2025-07-29 10:11:13 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 10:11:13 [DEBUG] 权限管理器初始化成功
2025-07-29 10:11:13 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:11:13 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:11:13 [INFO] 开始初始化UI权限管理
2025-07-29 10:11:13 [DEBUG] [实例ID: d05c247c] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:11:13 [DEBUG] 🔍 [实例ID: d05c247c] 字典引用一致性检查:
2025-07-29 10:11:13 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:11:13 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:11:13 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:11:13 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:11:13 [DEBUG] 控件权限管理器初始化完成 [实例ID: d05c247c]
2025-07-29 10:11:13 [DEBUG] 开始注册控件权限映射
2025-07-29 10:11:13 [INFO] 开始初始化全局控件映射
2025-07-29 10:11:13 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 10:11:13 [DEBUG] 开始生成控件标题映射
2025-07-29 10:11:13 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:11:13 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:11:13 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:13 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:11:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:11:13 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:11:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:11:13 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:11:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:11:13 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:13 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:11:14 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 10:11:14 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 10:11:14 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:11:14 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 10:11:14 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 10:11:14 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 10:11:14 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 10:11:14 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 10:11:14 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 10:11:14 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 10:11:14 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 10:11:14 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 10:11:14 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 10:11:14 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 10:11:14 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 10:11:14 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 10:11:14 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 10:11:14 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 10:11:14 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 10:11:14 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 10:11:14 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 10:11:14 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 10:11:14 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 10:11:14 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 10:11:14 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 10:11:14 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 10:11:14 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 10:11:14 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 10:11:14 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 10:11:14 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 10:11:14 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 10:11:14 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 10:11:14 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 10:11:14 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 10:11:14 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 10:11:14 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 10:11:14 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 10:11:14 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 10:11:14 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 10:11:14 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 10:11:14 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 10:11:14 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 10:11:14 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 10:11:14 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 10:11:14 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 10:11:14 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 10:11:14 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 10:11:14 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 10:11:14 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 10:11:14 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 10:11:14 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 10:11:14 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 10:11:14 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 10:11:14 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 10:11:14 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 10:11:14 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 10:11:14 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 10:11:14 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 10:11:14 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 10:11:14 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 10:11:14 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 10:11:14 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 10:11:14 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 10:11:14 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 10:11:14 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 10:11:14 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 10:11:14 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 10:11:14 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 10:11:14 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 10:11:14 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 10:11:14 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 10:11:14 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 10:11:14 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 10:11:14 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 10:11:14 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 10:11:14 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 10:11:14 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 10:11:14 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 10:11:14 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 10:11:14 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 10:11:14 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 10:11:14 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 10:11:14 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 10:11:14 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 10:11:14 [DEBUG] 开始生成控件权限映射
2025-07-29 10:11:14 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:11:14 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:11:14 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:11:14 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:11:14 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:11:14 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:11:14 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:11:14 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:11:14 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:11:14 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:11:14 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 10:11:14 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 10:11:14 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 10:11:14 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 10:11:14 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 10:11:14 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:11:14 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:11:14 [INFO] 开始初始化权限验证
2025-07-29 10:11:14 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:11:14 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:11:14 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 10:11:14 [INFO] 启动网络授权信息获取任务
2025-07-29 10:11:14 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 10:11:14 [INFO] 所有权限检查完成
2025-07-29 10:11:14 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:11:14 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:14 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:14 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:14 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:11:14 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:11:14 [INFO] 权限验证初始化完成
2025-07-29 10:11:14 [INFO] UI权限管理初始化完成
2025-07-29 10:11:14 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:11:14 [INFO] 开始刷新控件标题
2025-07-29 10:11:14 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:11:14 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:11:14 [DEBUG] 控件标题刷新完成
2025-07-29 10:11:14 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:11:14 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:11:14 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:11:14 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:11:14 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:11:14 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:11:14 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:14 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:11:14 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:14 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:11:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:11:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:11:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:11:14 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:11:14 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:11:14 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:11:14 [INFO] 控件标题更正完成
2025-07-29 10:11:14 [INFO] 控件标题刷新完成
2025-07-29 10:11:14 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:11:14 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:11:14 [DEBUG] 授权验证初始化完成
2025-07-29 10:11:14 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 10:11:14 [INFO] 成功加载配置和授权信息
2025-07-29 10:11:14 [INFO] 开始初始化定时器和设置
2025-07-29 10:11:14 [INFO] 定时器和设置初始化完成
2025-07-29 10:11:14 [INFO] 开始VSTO插件启动流程
2025-07-29 10:11:14 [INFO] TopMostForm窗体加载完成
2025-07-29 10:11:14 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:11:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7018342
2025-07-29 10:11:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7018342)
2025-07-29 10:11:14 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7018342
2025-07-29 10:11:14 [INFO] 系统事件监控已启动
2025-07-29 10:11:14 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:11:14 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 10:11:14 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 10:11:14 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 10:11:14 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 18814294
2025-07-29 10:11:14 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 10:11:14 [INFO] VSTO插件启动流程完成
2025-07-29 10:11:15 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 10:11:15 [INFO] 网络授权信息已更新并触发回调
2025-07-29 10:11:15 [INFO] 网络授权信息已从 Network 更新
2025-07-29 10:11:15 [INFO] 授权版本: 1.0
2025-07-29 10:11:15 [INFO] 颁发者: ExtensionsTools
2025-07-29 10:11:15 [INFO] 用户数量: 2
2025-07-29 10:11:15 [INFO] 分组权限数量: 2
2025-07-29 10:11:15 [WARN] 配置文件中未找到用户组信息
2025-07-29 10:11:15 [INFO] 已重新设置用户组: []
2025-07-29 10:11:15 [INFO] 用户组信息已重新设置
2025-07-29 10:11:15 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 10:11:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:11:15 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 10:11:15 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 10:11:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:11:15 [DEBUG] 本地权限缓存已清空
2025-07-29 10:11:15 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 10:11:15 [INFO] 所有权限检查完成
2025-07-29 10:11:15 [DEBUG] 权限重新检查完成
2025-07-29 10:11:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:15 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:15 [INFO] UI界面权限状态已更新
2025-07-29 10:11:15 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:11:15 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:11:15 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 10:11:15 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 10:11:15 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 10:11:15 [INFO] 开始刷新Ribbon控件标题
2025-07-29 10:11:15 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 10:11:15 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 10:11:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:11:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:11:15 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:15 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:11:15 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:11:15 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:11:15 [INFO] 控件标题更正完成
2025-07-29 10:11:15 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 10:11:15 [INFO] Ribbon控件标题刷新完成
2025-07-29 10:11:15 [INFO] 控件标题刷新完成
2025-07-29 10:11:15 [DEBUG] Ribbon控件标题已刷新
2025-07-29 10:11:15 [INFO] 开始刷新控件标题
2025-07-29 10:11:15 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:11:15 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:11:15 [DEBUG] 控件标题刷新完成
2025-07-29 10:11:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:11:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:11:15 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:11:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:15 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:11:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:15 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:11:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:11:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:11:15 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:11:15 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:11:15 [INFO] 控件标题更正完成
2025-07-29 10:11:15 [INFO] 控件标题刷新完成
2025-07-29 10:11:15 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 10:11:15 [INFO] 开始刷新授权状态
2025-07-29 10:11:15 [DEBUG] 开始初始化授权验证
2025-07-29 10:11:15 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:11:15 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:11:15 [INFO] 开始初始化UI权限管理
2025-07-29 10:11:15 [DEBUG] [实例ID: 09a03b1e] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:11:15 [DEBUG] 🔍 [实例ID: 09a03b1e] 字典引用一致性检查:
2025-07-29 10:11:15 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:11:15 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:11:15 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:11:15 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:11:15 [DEBUG] 控件权限管理器初始化完成 [实例ID: 09a03b1e]
2025-07-29 10:11:15 [DEBUG] 开始注册控件权限映射
2025-07-29 10:11:15 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:11:15 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:11:15 [INFO] 开始初始化权限验证
2025-07-29 10:11:15 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:11:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:11:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7018342
2025-07-29 10:11:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7018342)
2025-07-29 10:11:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 10:11:16 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:16 [DEBUG] 开始重置 209 个命令栏
2025-07-29 10:11:16 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:11:16 [INFO] 所有权限检查完成
2025-07-29 10:11:16 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:16 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:11:16 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:11:16 [INFO] 权限验证初始化完成
2025-07-29 10:11:16 [INFO] UI权限管理初始化完成
2025-07-29 10:11:16 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:16 [INFO] 开始刷新控件标题
2025-07-29 10:11:16 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:11:16 [DEBUG] 控件标题刷新完成
2025-07-29 10:11:16 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:11:16 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:11:16 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:11:16 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:11:16 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:11:16 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:11:16 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:11:16 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:11:16 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:11:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:11:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:11:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:11:16 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:11:16 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:11:16 [INFO] 控件标题更正完成
2025-07-29 10:11:16 [INFO] 控件标题刷新完成
2025-07-29 10:11:16 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:11:16 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:11:16 [DEBUG] 授权验证初始化完成
2025-07-29 10:11:16 [INFO] 授权状态刷新完成
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:16 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:16 [DEBUG] 重置命令栏: cell
2025-07-29 10:11:16 [DEBUG] 重置命令栏: column
2025-07-29 10:11:16 [DEBUG] 重置命令栏: row
2025-07-29 10:11:16 [DEBUG] 重置命令栏: cell
2025-07-29 10:11:16 [DEBUG] 重置命令栏: column
2025-07-29 10:11:16 [DEBUG] 重置命令栏: row
2025-07-29 10:11:16 [DEBUG] 重置命令栏: row
2025-07-29 10:11:16 [DEBUG] 重置命令栏: column
2025-07-29 10:11:17 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 10:11:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:18 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:18 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:11:18 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:11:18 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:18 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:11:18 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:11:18 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:11:18 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-07-29 10:11:18 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-07-29 10:11:18 [INFO] OpenForm: 准备打开窗体 '文件操作'，位置: Center，单实例: True
2025-07-29 10:11:18 [INFO] 开始显示窗体 '文件操作'，位置模式: Center
2025-07-29 10:11:18 [INFO] 窗体 '文件操作' 以TopMostForm为父窗体显示
2025-07-29 10:11:18 [INFO] 窗体 '文件操作' 显示完成，句柄: 21109810
2025-07-29 10:11:18 [INFO] OpenForm: 窗体 '文件操作' 打开成功
2025-07-29 10:11:18 [INFO] OpenForm: 准备打开窗体 '文件操作'，位置: Center，单实例: True
2025-07-29 10:11:18 [INFO] OpenForm: 窗体 '文件操作' 已存在，激活现有实例
2025-07-29 10:11:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:19 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:19 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:20 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:20 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:20 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:20 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:21 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:21 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:11:21 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:11:21 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:11:21 [INFO] 窗体 '备份及发送' 显示完成，句柄: 47383536
2025-07-29 10:11:21 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 10:11:21 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:21 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:21 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:22 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:22 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:23 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:11:23 [DEBUG] 授权控制器已初始化
2025-07-29 10:11:23 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:11:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:11:23 [DEBUG] 已重置工作表标签菜单
2025-07-29 10:11:23 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:28 [INFO] Excel窗口句柄监控器初始化完成
2025-07-29 10:21:28 [INFO] 配置文件实例已在加载时初始化
2025-07-29 10:21:28 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-29 10:21:28 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-29 10:21:28 [INFO] 🔍 znAbout控件实例: 存在
2025-07-29 10:21:28 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-29 10:21:28 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-29 10:21:28 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:21:28 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-29 10:21:28 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-29 10:21:28 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-29 10:21:28 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:21:28 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-29 10:21:28 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-29 10:21:28 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-29 10:21:28 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:21:28 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-29 10:21:28 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-29 10:21:28 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-29 10:21:28 [INFO] 成功初始化Excel应用程序实例
2025-07-29 10:21:28 [INFO] 自动备份路径未配置
2025-07-29 10:21:28 [DEBUG] 开始初始化授权控制器
2025-07-29 10:21:28 [DEBUG] 授权系统初始化完成，耗时: 370ms
2025-07-29 10:21:28 [DEBUG] 开始初始化授权验证
2025-07-29 10:21:28 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-29 10:21:28 [DEBUG] 权限管理器初始化成功
2025-07-29 10:21:28 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:21:28 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:21:28 [INFO] 开始初始化UI权限管理
2025-07-29 10:21:28 [DEBUG] [实例ID: 771fed3f] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:21:28 [DEBUG] 🔍 [实例ID: 771fed3f] 字典引用一致性检查:
2025-07-29 10:21:28 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:21:28 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:21:28 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:21:28 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:21:28 [DEBUG] 控件权限管理器初始化完成 [实例ID: 771fed3f]
2025-07-29 10:21:28 [DEBUG] 开始注册控件权限映射
2025-07-29 10:21:28 [INFO] 开始初始化全局控件映射
2025-07-29 10:21:28 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-29 10:21:28 [DEBUG] 开始生成控件标题映射
2025-07-29 10:21:28 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:21:28 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:21:28 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:28 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:21:29 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-29 10:21:29 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-29 10:21:29 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:21:29 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-29 10:21:29 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-29 10:21:29 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-29 10:21:29 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-29 10:21:29 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-29 10:21:29 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-29 10:21:29 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-29 10:21:29 [INFO] === znAbout控件标题映射诊断 ===
2025-07-29 10:21:29 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-29 10:21:29 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-29 10:21:29 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-29 10:21:29 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-29 10:21:29 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-29 10:21:29 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-29 10:21:29 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-29 10:21:29 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-29 10:21:29 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-29 10:21:29 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-29 10:21:29 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-29 10:21:29 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-29 10:21:29 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-29 10:21:29 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-29 10:21:29 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-29 10:21:29 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-29 10:21:29 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-29 10:21:29 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-29 10:21:29 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-29 10:21:29 [DEBUG] 控件映射: button3 -> '关于'
2025-07-29 10:21:29 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-29 10:21:29 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-29 10:21:29 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-29 10:21:29 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-29 10:21:29 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-29 10:21:29 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-29 10:21:29 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-29 10:21:29 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-29 10:21:29 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-29 10:21:29 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-29 10:21:29 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-29 10:21:29 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-29 10:21:29 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-29 10:21:29 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-29 10:21:29 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-29 10:21:29 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-29 10:21:29 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-29 10:21:29 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-29 10:21:29 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-29 10:21:29 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-29 10:21:29 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-29 10:21:29 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-29 10:21:29 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-29 10:21:29 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-29 10:21:29 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-29 10:21:29 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-29 10:21:29 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-29 10:21:29 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-29 10:21:29 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-29 10:21:29 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-29 10:21:29 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-29 10:21:29 [DEBUG] 控件映射: group1 -> '关于'
2025-07-29 10:21:29 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-29 10:21:29 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-29 10:21:29 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-29 10:21:29 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-29 10:21:29 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-29 10:21:29 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-29 10:21:29 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-29 10:21:29 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-29 10:21:29 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-29 10:21:29 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-29 10:21:29 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-29 10:21:29 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-29 10:21:29 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-29 10:21:29 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-29 10:21:29 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-29 10:21:29 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-29 10:21:29 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-29 10:21:29 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-29 10:21:29 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-29 10:21:29 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-29 10:21:29 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-29 10:21:29 [DEBUG] 开始生成控件权限映射
2025-07-29 10:21:29 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-29 10:21:29 [DEBUG] 通过反射获取到 112 个字段
2025-07-29 10:21:29 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-29 10:21:29 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-29 10:21:29 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-29 10:21:29 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-29 10:21:29 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-29 10:21:29 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-29 10:21:29 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-29 10:21:29 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-29 10:21:29 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-29 10:21:29 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-29 10:21:29 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-29 10:21:29 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:21:29 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:21:29 [INFO] 开始初始化权限验证
2025-07-29 10:21:29 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:21:29 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:21:29 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-29 10:21:29 [INFO] 启动网络授权信息获取任务
2025-07-29 10:21:29 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-29 10:21:29 [INFO] 所有权限检查完成
2025-07-29 10:21:29 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:21:29 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:29 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:29 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:29 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:29 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:29 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:21:29 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:21:29 [INFO] 权限验证初始化完成
2025-07-29 10:21:29 [INFO] UI权限管理初始化完成
2025-07-29 10:21:29 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:21:29 [INFO] 开始刷新控件标题
2025-07-29 10:21:29 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:21:29 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:21:29 [DEBUG] 控件标题刷新完成
2025-07-29 10:21:29 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:21:29 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:21:29 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:21:29 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:29 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:21:29 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:29 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:21:29 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:29 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:21:29 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:29 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:21:29 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:29 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:21:29 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:21:29 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:21:29 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:21:29 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:21:29 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:21:29 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:21:29 [INFO] 控件标题更正完成
2025-07-29 10:21:29 [INFO] 控件标题刷新完成
2025-07-29 10:21:29 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:21:29 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:21:29 [DEBUG] 授权验证初始化完成
2025-07-29 10:21:29 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-07-29 10:21:29 [INFO] 成功加载配置和授权信息
2025-07-29 10:21:29 [INFO] 开始初始化定时器和设置
2025-07-29 10:21:29 [INFO] 定时器和设置初始化完成
2025-07-29 10:21:29 [INFO] 开始VSTO插件启动流程
2025-07-29 10:21:30 [INFO] TopMostForm窗体加载完成
2025-07-29 10:21:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:21:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12261148
2025-07-29 10:21:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12261148)
2025-07-29 10:21:30 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 12261148
2025-07-29 10:21:30 [INFO] 系统事件监控已启动
2025-07-29 10:21:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:21:30 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-29 10:21:30 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-29 10:21:30 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-29 10:21:30 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 7936278
2025-07-29 10:21:30 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-29 10:21:30 [INFO] VSTO插件启动流程完成
2025-07-29 10:21:30 [INFO] 从Remote成功获取到网络授权信息
2025-07-29 10:21:30 [INFO] 网络授权信息已更新并触发回调
2025-07-29 10:21:30 [INFO] 网络授权信息已从 Network 更新
2025-07-29 10:21:30 [INFO] 授权版本: 1.0
2025-07-29 10:21:30 [INFO] 颁发者: ExtensionsTools
2025-07-29 10:21:30 [INFO] 用户数量: 2
2025-07-29 10:21:30 [INFO] 分组权限数量: 2
2025-07-29 10:21:30 [WARN] 配置文件中未找到用户组信息
2025-07-29 10:21:30 [INFO] 已重新设置用户组: []
2025-07-29 10:21:30 [INFO] 用户组信息已重新设置
2025-07-29 10:21:30 [INFO] 立即刷新权限缓存和UI界面
2025-07-29 10:21:30 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:21:30 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-29 10:21:30 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-29 10:21:30 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-29 10:21:30 [DEBUG] 本地权限缓存已清空
2025-07-29 10:21:30 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-29 10:21:30 [INFO] 所有权限检查完成
2025-07-29 10:21:30 [DEBUG] 权限重新检查完成
2025-07-29 10:21:30 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:30 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:30 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:30 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:30 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:30 [INFO] UI界面权限状态已更新
2025-07-29 10:21:30 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:21:30 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:21:30 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-29 10:21:30 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-29 10:21:30 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-29 10:21:30 [INFO] 开始刷新Ribbon控件标题
2025-07-29 10:21:30 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-29 10:21:30 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-29 10:21:30 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:21:30 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:21:30 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:30 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:21:30 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:21:30 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:21:30 [INFO] 控件标题更正完成
2025-07-29 10:21:30 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-29 10:21:30 [INFO] Ribbon控件标题刷新完成
2025-07-29 10:21:30 [INFO] 控件标题刷新完成
2025-07-29 10:21:30 [DEBUG] Ribbon控件标题已刷新
2025-07-29 10:21:30 [INFO] 开始刷新控件标题
2025-07-29 10:21:30 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:21:30 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:21:30 [DEBUG] 控件标题刷新完成
2025-07-29 10:21:30 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:21:30 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:21:30 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:21:30 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:30 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:21:30 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:30 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:21:30 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:21:30 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:21:30 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:21:30 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:21:30 [INFO] 控件标题更正完成
2025-07-29 10:21:30 [INFO] 控件标题刷新完成
2025-07-29 10:21:30 [DEBUG] Ribbon控件标题已立即刷新
2025-07-29 10:21:31 [INFO] 开始刷新授权状态
2025-07-29 10:21:31 [DEBUG] 开始初始化授权验证
2025-07-29 10:21:31 [DEBUG] 使用新的权限管理器进行初始化
2025-07-29 10:21:31 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-29 10:21:31 [INFO] 开始初始化UI权限管理
2025-07-29 10:21:31 [DEBUG] [实例ID: a2c42ca9] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-29 10:21:31 [DEBUG] 🔍 [实例ID: a2c42ca9] 字典引用一致性检查:
2025-07-29 10:21:31 [DEBUG] 🔍   标题映射一致性: True
2025-07-29 10:21:31 [DEBUG] 🔍   权限映射一致性: True
2025-07-29 10:21:31 [DEBUG] 🔍   信息映射一致性: True
2025-07-29 10:21:31 [DEBUG] 🔍   特殊控件一致性: True
2025-07-29 10:21:31 [DEBUG] 控件权限管理器初始化完成 [实例ID: a2c42ca9]
2025-07-29 10:21:31 [DEBUG] 开始注册控件权限映射
2025-07-29 10:21:31 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-29 10:21:31 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-29 10:21:31 [INFO] 开始初始化权限验证
2025-07-29 10:21:31 [DEBUG] 设置默认UI可见性为false
2025-07-29 10:21:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:21:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12261148
2025-07-29 10:21:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12261148)
2025-07-29 10:21:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-29 10:21:31 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:31 [DEBUG] 开始重置 209 个命令栏
2025-07-29 10:21:31 [DEBUG] 开始检查所有需要的权限
2025-07-29 10:21:31 [INFO] 所有权限检查完成
2025-07-29 10:21:31 [DEBUG] 应用权限状态到UI控件
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:31 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:31 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:31 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:31 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:31 [DEBUG] 启动后台权限刷新任务
2025-07-29 10:21:31 [DEBUG] 启动延迟权限刷新任务
2025-07-29 10:21:31 [INFO] 权限验证初始化完成
2025-07-29 10:21:31 [INFO] UI权限管理初始化完成
2025-07-29 10:21:31 [INFO] 收到权限管理器初始化完成通知
2025-07-29 10:21:31 [INFO] 开始刷新控件标题
2025-07-29 10:21:31 [DEBUG] 开始刷新所有控件权限状态
2025-07-29 10:21:31 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-29 10:21:31 [DEBUG] 控件标题刷新完成
2025-07-29 10:21:31 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-29 10:21:31 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-29 10:21:31 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-29 10:21:31 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:31 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-29 10:21:31 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:31 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-29 10:21:31 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:32 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:32 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-29 10:21:32 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:32 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:32 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-29 10:21:32 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:32 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:32 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:32 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-29 10:21:32 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:32 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-29 10:21:32 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-29 10:21:32 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-29 10:21:32 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-29 10:21:32 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-29 10:21:32 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-29 10:21:32 [INFO] 控件标题更正完成
2025-07-29 10:21:32 [INFO] 控件标题刷新完成
2025-07-29 10:21:32 [INFO] 权限管理器初始化完成处理结束
2025-07-29 10:21:32 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-29 10:21:32 [DEBUG] 授权验证初始化完成
2025-07-29 10:21:32 [INFO] 授权状态刷新完成
2025-07-29 10:21:32 [DEBUG] 重置命令栏: cell
2025-07-29 10:21:32 [DEBUG] 重置命令栏: column
2025-07-29 10:21:32 [DEBUG] 重置命令栏: row
2025-07-29 10:21:32 [DEBUG] 重置命令栏: cell
2025-07-29 10:21:32 [DEBUG] 重置命令栏: column
2025-07-29 10:21:32 [DEBUG] 重置命令栏: row
2025-07-29 10:21:32 [DEBUG] 重置命令栏: row
2025-07-29 10:21:32 [DEBUG] 重置命令栏: column
2025-07-29 10:21:32 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-29 10:21:33 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:33 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:33 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:33 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:33 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-29 10:21:33 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-29 10:21:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:33 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-29 10:21:33 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-29 10:21:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-29 10:21:33 [DEBUG] 已应用权限状态到UI控件
2025-07-29 10:21:34 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:34 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:34 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:34 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:34 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:34 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:34 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:35 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:35 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:35 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:35 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:35 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:35 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:35 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:35 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:36 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:36 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:36 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:36 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:36 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:36 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:36 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:36 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:37 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:37 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:21:37 [DEBUG] 授权控制器已初始化
2025-07-29 10:21:37 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-29 10:21:37 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-29 10:21:37 [DEBUG] 已重置工作表标签菜单
2025-07-29 10:21:37 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-29 10:22:36 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:22:36 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:22:36 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:22:36 [INFO] 窗体 '备份及发送' 显示完成，句柄: 16973928
2025-07-29 10:22:36 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
2025-07-29 10:22:43 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开事件触发
2025-07-29 10:22:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:22:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 12261148, 新父窗口: 6888708
2025-07-29 10:22:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 6888708)
2025-07-29 10:22:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:22:43 [INFO] App_WorkbookOpen: 工作簿 '骨头点黑点-20241122(2).xlsx' 打开处理完成
2025-07-29 10:22:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:22:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6888708)
2025-07-29 10:22:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:22:44 [INFO] App_WorkbookActivate: 工作簿 '骨头点黑点-20241122(2).xlsx' 激活处理完成
2025-07-29 10:22:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-29 10:22:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6888708)
2025-07-29 10:22:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-29 10:22:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-29 10:22:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:22:44 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:22:44 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-29 10:22:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:22:44 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:22:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-29 10:22:44 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 178
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-29 10:22:44 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-29 10:22:45 [WARN] 检测到Excel窗口句柄变化: 12261148 -> 6888708
2025-07-29 10:22:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 6888708)
2025-07-29 10:22:49 [INFO] OpenForm: 准备打开窗体 '备份及发送'，位置: Center，单实例: True
2025-07-29 10:22:49 [INFO] 开始显示窗体 '备份及发送'，位置模式: Center
2025-07-29 10:22:49 [INFO] 窗体 '备份及发送' 以TopMostForm为父窗体显示
2025-07-29 10:22:49 [INFO] 窗体 '备份及发送' 显示完成，句柄: 9378894
2025-07-29 10:22:49 [INFO] OpenForm: 窗体 '备份及发送' 打开成功
